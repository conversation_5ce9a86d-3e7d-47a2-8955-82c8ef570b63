"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Separator } from "@/components/ui/separator"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import { 
  User, 
  Mail, 
  Phone, 
  MapPin, 
  GraduationCap, 
  Users, 
  QrCode, 
  Edit, 
  Print, 
  Download,
  Calendar,
  TrendingUp,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle
} from "lucide-react"
import { Student, getFullName } from "@/lib/types/student"
import { QRCodeDisplay } from "./qr-code-display"
import { AttendanceStatistics } from "./attendance-statistics"
import { cn } from "@/lib/utils"

interface StudentProfileViewProps {
  student: Student
  onEdit?: () => void
  onGenerateQR?: () => void
  onPrintQR?: () => void
  className?: string
}

export function StudentProfileView({
  student,
  onEdit,
  onGenerateQR,
  onPrintQR,
  className
}: StudentProfileViewProps) {
  const [activeTab, setActiveTab] = useState("overview")

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Active':
        return 'bg-green-500'
      case 'Inactive':
        return 'bg-yellow-500'
      case 'Transferred':
        return 'bg-blue-500'
      case 'Graduated':
        return 'bg-purple-500'
      default:
        return 'bg-gray-500'
    }
  }

  const getAttendanceIcon = (rate: number) => {
    if (rate >= 95) return <CheckCircle className="h-4 w-4 text-green-500" />
    if (rate >= 85) return <AlertCircle className="h-4 w-4 text-yellow-500" />
    return <XCircle className="h-4 w-4 text-red-500" />
  }

  const fullName = getFullName(student)

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header Section */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col md:flex-row gap-6">
            {/* Student Photo and Basic Info */}
            <div className="flex flex-col items-center md:items-start gap-4">
              <Avatar className="h-32 w-32 border-4 border-border">
                <AvatarImage 
                  src={student.photo} 
                  alt={fullName}
                  className="object-cover"
                />
                <AvatarFallback className="text-2xl font-bold bg-primary/10">
                  {getInitials(fullName)}
                </AvatarFallback>
              </Avatar>
              
              <div className="text-center md:text-left">
                <Badge 
                  variant={student.status === 'Active' ? 'default' : 'secondary'}
                  className={cn("text-xs", getStatusColor(student.status))}
                >
                  {student.status}
                </Badge>
              </div>
            </div>

            {/* Student Details */}
            <div className="flex-1 space-y-4">
              <div>
                <h1 className="text-3xl font-bold">{fullName}</h1>
                <p className="text-lg text-muted-foreground">{student.email}</p>
                <p className="text-sm text-muted-foreground">ID: {student.id}</p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center gap-2">
                  <GraduationCap className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">
                    Grade {student.grade} - {student.section ? `Section ${student.section}` : 'No Section'}
                  </span>
                </div>
                
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">{student.course} - {student.year}</span>
                </div>
                
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">
                    Enrolled: {new Date(student.enrollmentDate).toLocaleDateString()}
                  </span>
                </div>
                
                {student.attendanceStats && (
                  <div className="flex items-center gap-2">
                    {getAttendanceIcon(student.attendanceStats.attendanceRate)}
                    <span className="text-sm">
                      Attendance: {student.attendanceStats.attendanceRate.toFixed(1)}%
                    </span>
                  </div>
                )}
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col gap-2">
              {onEdit && (
                <Button onClick={onEdit} variant="outline">
                  <Edit className="h-4 w-4 mr-2" />
                  Edit
                </Button>
              )}
              
              {onGenerateQR && (
                <Button onClick={onGenerateQR} variant="outline">
                  <QrCode className="h-4 w-4 mr-2" />
                  Generate QR
                </Button>
              )}
              
              {onPrintQR && (
                <Button onClick={onPrintQR} variant="outline">
                  <Print className="h-4 w-4 mr-2" />
                  Print QR
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Detailed Information Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="contacts">Contacts</TabsTrigger>
          <TabsTrigger value="attendance">Attendance</TabsTrigger>
          <TabsTrigger value="qr-code">QR Code</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Personal Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  Personal Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <span className="text-muted-foreground">First Name:</span>
                  <span>{student.firstName}</span>
                  
                  {student.middleName && (
                    <>
                      <span className="text-muted-foreground">Middle Name:</span>
                      <span>{student.middleName}</span>
                    </>
                  )}
                  
                  <span className="text-muted-foreground">Last Name:</span>
                  <span>{student.lastName}</span>
                  
                  {student.dateOfBirth && (
                    <>
                      <span className="text-muted-foreground">Date of Birth:</span>
                      <span>{new Date(student.dateOfBirth).toLocaleDateString()}</span>
                    </>
                  )}
                  
                  {student.gender && (
                    <>
                      <span className="text-muted-foreground">Gender:</span>
                      <span>{student.gender}</span>
                    </>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Academic Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <GraduationCap className="h-5 w-5" />
                  Academic Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <span className="text-muted-foreground">Grade Level:</span>
                  <span>Grade {student.grade}</span>
                  
                  <span className="text-muted-foreground">Section:</span>
                  <span>{student.section || 'Not assigned'}</span>
                  
                  <span className="text-muted-foreground">Course/Track:</span>
                  <span>{student.course}</span>
                  
                  <span className="text-muted-foreground">Year Level:</span>
                  <span>{student.year}</span>
                  
                  <span className="text-muted-foreground">Status:</span>
                  <Badge variant={student.status === 'Active' ? 'default' : 'secondary'}>
                    {student.status}
                  </Badge>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Address Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MapPin className="h-5 w-5" />
                Address Information
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-sm">
                <p>{student.address.street}</p>
                <p>Barangay {student.address.barangay}</p>
                <p>{student.address.city}, {student.address.province} {student.address.zipCode}</p>
                <p>{student.address.country}</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Contacts Tab */}
        <TabsContent value="contacts" className="space-y-6">
          {/* Guardian Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Guardian Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p className="font-medium">{student.guardian.name}</p>
                  <p className="text-sm text-muted-foreground">{student.guardian.relationship}</p>
                </div>
                
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Phone className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">{student.guardian.phone}</span>
                  </div>
                  
                  {student.guardian.email && (
                    <div className="flex items-center gap-2">
                      <Mail className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">{student.guardian.email}</span>
                    </div>
                  )}
                </div>
              </div>
              
              {student.guardian.address && (
                <div className="pt-2 border-t">
                  <p className="text-sm text-muted-foreground">Address:</p>
                  <p className="text-sm">{student.guardian.address}</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Emergency Contacts */}
          <Card>
            <CardHeader>
              <CardTitle>Emergency Contacts</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {student.emergencyContacts.map((contact, index) => (
                  <div key={index} className="p-4 border rounded-lg">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <p className="font-medium">{contact.name}</p>
                        <p className="text-sm text-muted-foreground">{contact.relationship}</p>
                      </div>
                      
                      <div className="space-y-1">
                        <div className="flex items-center gap-2">
                          <Phone className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm">{contact.phone}</span>
                        </div>
                        
                        {contact.address && (
                          <div className="flex items-center gap-2">
                            <MapPin className="h-4 w-4 text-muted-foreground" />
                            <span className="text-sm">{contact.address}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Attendance Tab */}
        <TabsContent value="attendance" className="space-y-6">
          <AttendanceStatistics
            student={student}
            showDetailed={true}
            showActions={true}
          />
        </TabsContent>

        {/* QR Code Tab */}
        <TabsContent value="qr-code" className="space-y-6">
          <QRCodeDisplay
            student={student}
            onQRGenerated={onGenerateQR}
            onQRRegenerated={onGenerateQR}
          />
        </TabsContent>
      </Tabs>
    </div>
  )
}
