{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_da5a9c44._.js", "server/edge/chunks/node_modules_@auth_core_ec7fbf6d._.js", "server/edge/chunks/node_modules_jose_dist_webapi_49ff121e._.js", "server/edge/chunks/node_modules_eb9c5e08._.js", "server/edge/chunks/[root-of-the-server]__54fca45c._.js", "server/edge/chunks/edge-wrapper_4a07d54b.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "lNDM2CXPBhWttcW7WobUNEeeifmBiuxwGh+NkJdYoOg=", "__NEXT_PREVIEW_MODE_ID": "c6b1d2e8cd2179a04d56cddd2b60d262", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "c158b54cb7f0448ac5f19f6889e2d933dd17cb1d70df45fc3557190f431396d9", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "9558b7804ee799b312a8d584f024c1ab4a2946e3cbb2726ccdfcc335a1a782d0"}}}, "sortedMiddleware": ["/"], "functions": {}}